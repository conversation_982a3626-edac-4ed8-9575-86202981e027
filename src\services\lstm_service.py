#!/usr/bin/env python3
"""
Intel-Optimized LSTM Stock Prediction Service
Predicts 2-day ahead stock price direction using 'Adj Close' and 'Volume' features
Uses 3.5-year training windows for consistency with fine-tuning service
"""

import os
import sys
import json
import logging
from datetime import date, timedelta
from pathlib import Path

# Module-level constants - will be calculated dynamically for 3.5-year training window
PRED_DATES = [date(2025, 6, 4), date(2025, 6, 5)]

# Intel optimization environment variables
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['KMP_AFFINITY'] = 'granularity=fine,compact,1,0'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
os.environ['TF_ENABLE_BF16_CONVOLUTIONS'] = '1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['PYTHONWARNINGS'] = 'ignore'

# Enable oneDNN verbose if debug flag is set
if os.environ.get('LSTM_DEBUG_ONEDNN') == '1':
    os.environ['ONEDNN_VERBOSE'] = '1'

# Suppress warnings
import warnings
warnings.filterwarnings('ignore')

# Import required libraries
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from sklearn.utils.class_weight import compute_class_weight
from sklearn.isotonic import IsotonicRegression
from sklearn.metrics import roc_curve
import joblib
import tensorflow as tf
from tensorflow.keras import Sequential
from tensorflow.keras.layers import Dense, LSTM, Dropout
from tensorflow.keras.regularizers import l2
from tensorflow.keras.callbacks import EarlyStopping
from tensorflow.keras.optimizers import AdamW

# Common Constants for Technical Indicator-based LSTM Prediction
WINDOW_DAYS = 75        # LSTM input sequence length (75 trading days)
HORIZON_DAYS = 5        # Prediction horizon (5 business days ahead)
RSI_PERIOD = 14         # RSI calculation period
BB_PERIOD = 20          # Bollinger Bands period
BB_STD_MULT = 2         # Bollinger Bands standard deviation multiplier
TL_WINDOW = 5           # Traffic light performance window (last 5 predictions)

# Intel optimizations enabled via environment variables

# Configure TensorFlow threading for Intel Core Ultra 7 155H
tf.config.threading.set_intra_op_parallelism_threads(16)
tf.config.threading.set_inter_op_parallelism_threads(2)
tf.get_logger().setLevel('ERROR')

# Print startup log
print(f"✅  TensorFlow-Intel {tf.__version__} — oneDNN enabled", file=sys.stderr)


def compute_indicators(df):
    """
    Compute technical indicators for LSTM features

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with columns ['Date', 'close', 'volume'] (or similar)

    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    """
    # Ensure we have the required columns
    if 'Adj Close' in df.columns:
        df = df.rename(columns={'Adj Close': 'close'})
    if 'Volume' in df.columns:
        df = df.rename(columns={'Volume': 'volume'})

    # Sort by date to ensure proper calculation
    df = df.sort_values('Date').copy()

    # Calculate RSI(14)
    delta = df['close'].diff()
    gain = delta.clip(lower=0)
    loss = -delta.clip(upper=0)

    # Use exponential moving average for RSI calculation
    avg_gain = gain.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    avg_loss = loss.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    # Normalize RSI from 0-100 range to 0-1 scale
    df['rsi14'] = rsi / 100.0

    # Calculate Bollinger Bands
    sma = df['close'].rolling(window=BB_PERIOD).mean()
    std = df['close'].rolling(window=BB_PERIOD).std()
    bb_upper = sma + (BB_STD_MULT * std)
    bb_lower = sma - (BB_STD_MULT * std)

    # Calculate Bollinger Band features as percentages
    # bb_upper_pct: How far current price is above upper band (as percentage)
    df['bb_upper_pct'] = ((df['close'] - bb_upper) / bb_upper * 100).clip(lower=-100, upper=100)

    # bb_lower_pct: How far current price is above lower band (as percentage)
    df['bb_lower_pct'] = ((df['close'] - bb_lower) / bb_lower * 100).clip(lower=-100, upper=100)

    # bb_width_pct: Band width as percentage of middle line
    df['bb_width_pct'] = ((bb_upper - bb_lower) / sma * 100).clip(lower=0, upper=100)

    # Remove NaN values
    df = df.dropna()

    # Debug logging for technical indicator statistics
    print(f"Technical indicator stats:", file=sys.stderr)
    print(f"  RSI14 range: {df['rsi14'].min():.3f} - {df['rsi14'].max():.3f}", file=sys.stderr)
    print(f"  BB Upper %: {df['bb_upper_pct'].min():.1f} - {df['bb_upper_pct'].max():.1f}", file=sys.stderr)
    print(f"  BB Lower %: {df['bb_lower_pct'].min():.1f} - {df['bb_lower_pct'].max():.1f}", file=sys.stderr)
    print(f"  BB Width %: {df['bb_width_pct'].min():.1f} - {df['bb_width_pct'].max():.1f}", file=sys.stderr)

    # Return DataFrame with exact column order required
    feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    return df[feature_cols]


def update_traffic_light_history(ticker, is_correct, target_date):
    """
    Update traffic light performance history for a ticker

    Parameters:
    -----------
    ticker : str
        Stock ticker symbol
    is_correct : int
        1 if prediction was correct, 0 if incorrect
    target_date : date
        Date of the prediction

    Returns:
    --------
    str
        Traffic light color: 'green', 'yellow', or 'red'
    """
    # Create traffic_history directory if it doesn't exist
    ROOT_DIR = Path(__file__).resolve().parents[1]
    history_dir = ROOT_DIR / "data" / "traffic_history"
    history_dir.mkdir(parents=True, exist_ok=True)

    # Path to ticker's history file
    history_file = history_dir / f"{ticker}.csv"

    # Load existing history or create new DataFrame
    if history_file.exists():
        try:
            history_df = pd.read_csv(history_file)
            history_df['date'] = pd.to_datetime(history_df['date']).dt.date
        except Exception as e:
            print(f"Error reading history file {history_file}: {e}", file=sys.stderr)
            history_df = pd.DataFrame(columns=['date', 'is_correct'])
    else:
        history_df = pd.DataFrame(columns=['date', 'is_correct'])

    # Add new record
    new_record = pd.DataFrame({
        'date': [target_date],
        'is_correct': [is_correct]
    })
    history_df = pd.concat([history_df, new_record], ignore_index=True)

    # Keep only last TL_WINDOW (5) rows
    history_df = history_df.tail(TL_WINDOW)

    # Save updated history
    history_df.to_csv(history_file, index=False)

    # Calculate score and determine traffic light
    score = history_df['is_correct'].sum()

    if len(history_df) < TL_WINDOW:
        # Default to yellow if history length < TL_WINDOW
        traffic_light = "yellow"
    elif score >= 4:
        # 4-5 correct predictions = green
        traffic_light = "green"
    elif score >= 2:
        # 2-3 correct predictions = yellow
        traffic_light = "yellow"
    else:
        # 0-1 correct predictions = red
        traffic_light = "red"

    print(f"Traffic-light score: {score} – {traffic_light}", file=sys.stderr)

    return traffic_light


def load_and_prepare_data(ticker, use_volume=False):
    """Load data and prepare for training with 3.5-year training window"""
    try:
        # Load data
        ROOT_DIR = Path(__file__).resolve().parents[1]              # …/financial_dashboard
        csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

        if not csv_path.exists():
            print(f"Data file not found: {csv_path}", file=sys.stderr)
            sys.exit(1)

        # Read price data with proper data types
        df = pd.read_csv(csv_path)

        # Parse Date column
        df['Date'] = pd.to_datetime(df['Date']).dt.date

        # Convert numeric columns to float64
        for col in df.columns:
            if col != 'Date':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Check if ticker exists
        if ticker not in df.columns:
            print(f"Ticker {ticker} not found in data", file=sys.stderr)
            sys.exit(1)

        # Calculate training end date (3.5 years before first prediction date)
        train_end_date = PRED_DATES[0] - timedelta(days=1)  # Day before first prediction
        train_start_date = PRED_DATES[0] - timedelta(days=int(3.5 * 365))  # 3.5 years before

        # Load volume data if requested
        volume_data = None
        if use_volume:
            volume_path = ROOT_DIR / "data" / "sp500_volume_3y.csv"
            if not volume_path.exists():
                print(f"Volume data file not found: {volume_path}", file=sys.stderr)
                sys.exit(1)

            volume_df = pd.read_csv(volume_path)
            volume_df['Date'] = pd.to_datetime(volume_df['Date']).dt.date

            # Convert numeric columns to float64
            for col in volume_df.columns:
                if col != 'Date':
                    volume_df[col] = pd.to_numeric(volume_df[col], errors='coerce')

            if ticker not in volume_df.columns:
                print(f"Ticker {ticker} not found in volume data", file=sys.stderr)
                sys.exit(1)

            volume_data = volume_df[['Date', ticker]].copy()
            volume_data.columns = ['Date', 'Volume']

        # Filter training data using 3.5-year window
        train_data = df[(df['Date'] >= train_start_date) & (df['Date'] <= train_end_date)].copy()

        # Check minimum rows requirement
        if len(train_data) < 100:  # Increased minimum for 3.5-year window
            print(f"Insufficient training data: {len(train_data)} rows (minimum 100 required)", file=sys.stderr)
            sys.exit(1)

        # Get all data for predictions
        all_data = df[['Date', ticker]].copy()
        all_data.columns = ['Date', 'Adj Close']
        all_data = all_data.dropna().sort_values('Date')

        # Merge volume data if available
        if use_volume and volume_data is not None:
            volume_data = volume_data.dropna().sort_values('Date')
            all_data = pd.merge(all_data, volume_data, on='Date', how='inner')

            # Also prepare training data with volume
            train_volume = volume_data[
                (volume_data['Date'] >= train_start_date) &
                (volume_data['Date'] <= train_end_date)
            ].copy()
            train_data_with_volume = pd.merge(
                train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}),
                train_volume,
                on='Date',
                how='inner'
            )
            return all_data, train_data_with_volume, train_end_date
        else:
            return all_data, train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}), train_end_date

    except Exception as e:
        print(f"Error loading data: {e}", file=sys.stderr)
        sys.exit(1)


def create_sequences(df, feature_cols=None):
    """
    Create sequences for LSTM training with technical indicators

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with technical indicator features
    feature_cols : list, optional
        List of feature column names. If None, uses all columns except 'Date'

    Returns:
    --------
    tuple
        (X, y) where X has shape (samples, WINDOW_DAYS, num_features) and y is binary labels
    """
    if feature_cols is None:
        feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']

    X, y = [], []

    # Iterate from 0 to (len(df) - WINDOW_DAYS - HORIZON_DAYS)
    for i in range(len(df) - WINDOW_DAYS - HORIZON_DAYS):
        # Extract sequence from df.iloc[i:i+WINDOW_DAYS, feature_cols] with shape (75, 6)
        sequence = df.iloc[i:i+WINDOW_DAYS][feature_cols].values
        X.append(sequence)

        # Calculate target index: t = i + WINDOW_DAYS - 1, target_date = t + HORIZON_DAYS
        t = i + WINDOW_DAYS - 1
        target_date = t + HORIZON_DAYS

        # Create binary label: 1 if close[target_date] > close[t], else 0
        current_price = df.iloc[t][feature_cols[0]]  # Use first feature column (close)
        future_price = df.iloc[target_date][feature_cols[0]]  # Use first feature column (close)
        y.append(1 if future_price > current_price else 0)

    return np.array(X), np.array(y)


def focal_loss(gamma=2.0):
    """Focal Loss to address class imbalance more effectively than class weights"""
    def loss(y_true, y_pred):
        bce = tf.keras.losses.binary_crossentropy(y_true, y_pred)
        p_t = y_true * y_pred + (1 - y_true) * (1 - y_pred)
        return tf.pow(1 - p_t, gamma) * bce
    return loss


def build_lstm_model(input_shape=(WINDOW_DAYS, 6)):
    """
    Build Intel-optimized LSTM classifier model with L2 regularization

    Parameters:
    -----------
    input_shape : tuple
        Input shape for LSTM, default (75, 6) for technical indicators

    Returns:
    --------
    keras.Model
        Compiled LSTM model
    """
    model = Sequential([
        LSTM(64,
             return_sequences=True,
             input_shape=input_shape,
             kernel_regularizer=l2(1e-4),
             recurrent_regularizer=l2(1e-4)),
        LSTM(64,
             return_sequences=False,
             kernel_regularizer=l2(1e-4),
             recurrent_regularizer=l2(1e-4)),
        Dropout(0.2),  # Increased dropout for 6 features

        Dense(128,
              activation='relu',
              kernel_regularizer=l2(1e-4)),

        Dense(1, activation='sigmoid',
              kernel_regularizer=l2(1e-4))
    ])

    model.compile(
        optimizer=AdamW(learning_rate=0.0008, weight_decay=1e-4),
        loss=focal_loss(gamma=2.0),
        metrics=['binary_accuracy']
    )

    return model


def get_calibrated_probability(model, iso_calibrator, sequence):
    """Get calibrated probability using Isotonic Regression with proper input type"""
    raw_prediction = model.predict(sequence, verbose=0)[0][0]
    calibrated_prob = iso_calibrator.predict(np.array([raw_prediction]))[0]
    return calibrated_prob


def predict_for_dates(model, scaler, all_data, pred_dates, use_volume=False, prediction_horizon=2, iso_calibrator=None, best_threshold=0.5):
    """Make predictions for specific dates with 2-day horizon"""
    predictions = []
    seen_predictions = set()  # Prevent duplicate predictions

    for pred_date in pred_dates:
        # Find the date in data
        date_mask = all_data['Date'] == pred_date
        if not date_mask.any():
            print(f"Date {pred_date} not found in data", file=sys.stderr)
            sys.exit(1)

        date_idx = all_data[date_mask].index[0]

        # Get 60 days before this date for prediction
        if date_idx < 60:
            print(f"Insufficient data before {pred_date}", file=sys.stderr)
            sys.exit(1)

        # Check if we have enough future data for actual label calculation
        if date_idx + prediction_horizon - 1 >= len(all_data):
            print(f"Insufficient future data for {prediction_horizon}-day prediction at {pred_date}", file=sys.stderr)
            sys.exit(1)

        # Get sequence data
        if use_volume:
            # Get both price and volume data
            sequence_data = all_data.iloc[date_idx-60:date_idx][['Adj Close', 'Volume']].values
            sequence_scaled = scaler.transform(sequence_data)
            sequence_scaled = sequence_scaled.reshape(1, 60, 2)
        else:
            # Get price data only
            sequence_data = all_data.iloc[date_idx-60:date_idx]['Adj Close'].values
            sequence_scaled = scaler.transform(sequence_data.reshape(-1, 1))
            sequence_scaled = sequence_scaled.reshape(1, 60, 1)

        # Make prediction with calibration if available
        if iso_calibrator is not None:
            pred_prob_up = get_calibrated_probability(model, iso_calibrator, sequence_scaled)
            pred_prob_down = 1.0 - pred_prob_up
        else:
            pred_prob_up = model.predict(sequence_scaled, verbose=0)[0][0]
            pred_prob_down = 1.0 - pred_prob_up

        # Use optimal threshold for binary classification
        predicted_label = int(pred_prob_up > best_threshold)

        # Get actual value for 2-day horizon
        current_price = all_data.iloc[date_idx-1]['Adj Close']  # Price at prediction date
        future_price = all_data.iloc[date_idx + prediction_horizon - 1]['Adj Close']  # Price 2 days later
        actual_label = 1 if future_price > current_price else 0

        # Prevent duplicate predictions
        key = (pred_date, prediction_horizon)
        if key not in seen_predictions:
            predictions.append({
                "date": pred_date.strftime("%Y-%m-%d"),
                "pred_prob_up": round(float(pred_prob_up), 4),
                "pred_prob_down": round(float(pred_prob_down), 4),
                "predicted_label": int(predicted_label),
                "actual_label": int(actual_label),
                "prediction_horizon": prediction_horizon,
                "optimal_threshold": round(float(best_threshold), 4)
            })
            seen_predictions.add(key)

    return predictions


def determine_result_color(predictions):
    """Determine result color based on prediction accuracy"""
    correct_count = sum(1 for p in predictions if p["predicted_label"] == p["actual_label"])

    if correct_count == 2:
        return "green"
    elif correct_count == 1:
        return "yellow"
    else:
        return "red"


def main():
    """Main function"""
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python lstm_service.py <TICKER> [--no-volume]", file=sys.stderr)
        sys.exit(1)

    ticker = sys.argv[1].upper()
    # Enable volume by default, disable with --no-volume flag
    use_volume = True
    if len(sys.argv) == 3 and sys.argv[2] == '--no-volume':
        use_volume = False

    try:
        # Load and prepare data with 3.5-year training window
        all_data, train_data, train_end_date = load_and_prepare_data(ticker, use_volume)

        # Compute technical indicators on training data
        train_indicators = compute_indicators(train_data)

        print(f"Input shape: {train_indicators.shape}", file=sys.stderr)

        # Scale the features
        scaler = MinMaxScaler(feature_range=(0, 1))
        train_scaled = scaler.fit_transform(train_indicators.values)

        # Create sequences with technical indicators and HORIZON_DAYS prediction
        X_train, y_train = create_sequences(pd.DataFrame(train_scaled, columns=train_indicators.columns))

        print(f"Input shape: {X_train.shape}", file=sys.stderr)  # Should show (samples, 75, 6)
        print(f"Label balance: {y_train.mean():.3f}", file=sys.stderr)  # Should be ~0.5 ± 0.1

        # Build model for 6 features (close, volume, rsi14, bb_upper_pct, bb_lower_pct, bb_width_pct)
        model = build_lstm_model((WINDOW_DAYS, 6))

        # Manual time series validation split (preserve order)
        split_idx = int(len(X_train) * 0.9)
        X_tr, X_val = X_train[:split_idx], X_train[split_idx:]
        y_tr, y_val = y_train[:split_idx], y_train[split_idx:]

        # Calculate class weights to address imbalance
        class_weights = compute_class_weight('balanced', classes=np.array([0, 1]), y=y_tr)
        class_weight_dict = {0: class_weights[0], 1: class_weights[1]}

        # Early stopping callback
        early_stopping = EarlyStopping(
            monitor='val_binary_accuracy',
            patience=2,
            restore_best_weights=True
        )

        # Train with class weights and manual validation split
        model.fit(
            X_tr, y_tr,
            epochs=8,
            batch_size=256,
            validation_data=(X_val, y_val),
            callbacks=[early_stopping],
            class_weight=class_weight_dict,
            shuffle=False,  # Preserve time series order
            verbose=0
        )

        # Train Isotonic Regression for better calibration
        raw_val_predictions = model.predict(X_val, verbose=0).ravel()

        # Sort validation data by raw predictions for proper isotonic fitting
        sort_idx = np.argsort(raw_val_predictions)
        sorted_raw_preds = raw_val_predictions[sort_idx]
        sorted_y_val = y_val[sort_idx]

        # Train isotonic calibrator with sorted data
        iso_calibrator = IsotonicRegression(out_of_bounds='clip')
        iso_calibrator.fit(sorted_raw_preds, sorted_y_val)

        # Get calibrated validation predictions for threshold calculation
        calibrated_val_preds = iso_calibrator.predict(sorted_raw_preds)

        # Calculate optimal threshold using calibrated scores
        fpr, tpr, thresholds = roc_curve(sorted_y_val, calibrated_val_preds)
        best_threshold = thresholds[(tpr - fpr).argmax()]

        # Save model and calibrators with version tags
        ROOT_DIR = Path(__file__).resolve().parents[1]
        model_dir = ROOT_DIR / "data" / "lstm_results"
        model_dir.mkdir(parents=True, exist_ok=True)

        # File naming with version tags: w75_h5_rsi_bb
        version_tag = "w75_h5_rsi_bb"
        model.save(model_dir / f"{ticker}_model_{version_tag}.keras")
        joblib.dump(scaler, model_dir / f"{ticker}_scaler_{version_tag}.pkl")
        joblib.dump(iso_calibrator, model_dir / f"{ticker}_cal_{version_tag}.pkl")
        joblib.dump(best_threshold, model_dir / f"{ticker}_thr_{version_tag}.pkl")

        # Make predictions for target dates with technical indicators
        predictions = []
        for pred_date in PRED_DATES:
            # Compute indicators for all data
            all_indicators = compute_indicators(all_data)

            # Find prediction date
            date_mask = all_data['Date'] == pred_date
            if not date_mask.any():
                print(f"Date {pred_date} not found in data", file=sys.stderr)
                continue

            date_idx = all_data[date_mask].index[0]

            # Check data availability
            if date_idx < WINDOW_DAYS or date_idx + HORIZON_DAYS - 1 >= len(all_data):
                print(f"Insufficient data for prediction at {pred_date}", file=sys.stderr)
                continue

            # Extract sequence and make prediction
            sequence_data = all_indicators.iloc[date_idx-WINDOW_DAYS:date_idx].values
            sequence_scaled = scaler.transform(sequence_data)
            sequence_scaled = sequence_scaled.reshape(1, WINDOW_DAYS, 6)

            # Get prediction
            if iso_calibrator is not None:
                pred_prob_up = get_calibrated_probability(model, iso_calibrator, sequence_scaled)
            else:
                pred_prob_up = model.predict(sequence_scaled, verbose=0)[0][0]

            pred_prob_down = 1.0 - pred_prob_up
            predicted_label = int(pred_prob_up > best_threshold)

            # Calculate actual label
            current_price = all_data.iloc[date_idx-1]['Adj Close']
            future_price = all_data.iloc[date_idx + HORIZON_DAYS - 1]['Adj Close']
            actual_label = 1 if future_price > current_price else 0

            # Update traffic light history
            is_correct = int(predicted_label == actual_label)
            traffic_light = update_traffic_light_history(ticker, is_correct, pred_date)

            predictions.append({
                "date": pred_date.strftime("%Y-%m-%d"),
                "pred_prob_up": round(float(pred_prob_up), 4),
                "pred_prob_down": round(float(pred_prob_down), 4),
                "predicted_label": int(predicted_label),
                "actual_label": int(actual_label),
                "prediction_horizon": HORIZON_DAYS,
                "optimal_threshold": round(float(best_threshold), 4),
                "traffic_light": traffic_light
            })

        # Determine result color based on traffic light
        if predictions:
            # Use the traffic light from the last prediction
            result_color = predictions[-1]["traffic_light"]
        else:
            result_color = "yellow"

        # Add result color to each prediction
        for pred in predictions:
            pred["result_color"] = result_color

        # Create result
        result = {
            "symbol": ticker,
            "train_until": train_end_date.strftime("%Y-%m-%d"),
            "predictions": predictions,
            "traffic_light": result_color
        }



        # Print JSON result to stdout
        print(json.dumps(result))

        # Log completion
        logging.info(f"[{ticker}] LSTM completed – result: {result_color}")

    except Exception as e:
        print(f"Error in main execution: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()