#!/usr/bin/env python3
"""
Intel-Optimized LSTM Stock Prediction Service for Fine-tuning
Flexible date selection with 3.5-year training windows
Uses 'Adj Close' and 'Volume' features with 2-day prediction horizon
"""

import os
import sys
import json
import logging
import random
import re
import argparse
from datetime import date, timedelta, datetime
from pathlib import Path

# Intel optimization environment variables
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['KMP_AFFINITY'] = 'granularity=fine,compact,1,0'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
os.environ['TF_ENABLE_BF16_CONVOLUTIONS'] = '1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['PYTHONWARNINGS'] = 'ignore'

# Suppress warnings
import warnings
warnings.filterwarnings('ignore')

# TensorFlow imports with Intel optimizations
try:
    import tensorflow as tf
    tf.get_logger().setLevel('ERROR')
    
    # Enable Intel optimizations
    tf.config.threading.set_intra_op_parallelism_threads(16)
    tf.config.threading.set_inter_op_parallelism_threads(16)
    
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.optimizers import AdamW
    from tensorflow.keras.regularizers import l2
    from tensorflow.keras.callbacks import EarlyStopping
    
    print("✅  TensorFlow-Intel 2.18.0 — oneDNN enabled", file=sys.stderr)
    
except ImportError as e:
    print(f"TensorFlow import error: {e}", file=sys.stderr)
    sys.exit(1)

import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.utils.class_weight import compute_class_weight
from sklearn.isotonic import IsotonicRegression
from sklearn.metrics import roc_curve
import joblib

# Common Constants for Technical Indicator-based LSTM Prediction
WINDOW_DAYS = 75        # LSTM input sequence length (75 trading days)
HORIZON_DAYS = 5        # Prediction horizon (5 business days ahead)
RSI_PERIOD = 14         # RSI calculation period
BB_PERIOD = 20          # Bollinger Bands period
BB_STD_MULT = 2         # Bollinger Bands standard deviation multiplier
TL_WINDOW = 5           # Traffic light performance window (last 5 predictions)


def compute_indicators(df):
    """
    Compute technical indicators for LSTM features

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with columns ['Date', 'close', 'volume'] (or similar)

    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    """
    # Ensure we have the required columns
    if 'Adj Close' in df.columns:
        df = df.rename(columns={'Adj Close': 'close'})
    if 'Volume' in df.columns:
        df = df.rename(columns={'Volume': 'volume'})

    # Sort by date to ensure proper calculation
    df = df.sort_values('Date').copy()

    # Calculate RSI(14)
    delta = df['close'].diff()
    gain = delta.clip(lower=0)
    loss = -delta.clip(upper=0)

    # Use exponential moving average for RSI calculation
    avg_gain = gain.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    avg_loss = loss.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    # Normalize RSI from 0-100 range to 0-1 scale
    df['rsi14'] = rsi / 100.0

    # Calculate Bollinger Bands
    sma = df['close'].rolling(window=BB_PERIOD).mean()
    std = df['close'].rolling(window=BB_PERIOD).std()
    bb_upper = sma + (BB_STD_MULT * std)
    bb_lower = sma - (BB_STD_MULT * std)

    # Calculate Bollinger Band features as percentages
    # bb_upper_pct: How far current price is above upper band (as percentage)
    df['bb_upper_pct'] = ((df['close'] - bb_upper) / bb_upper * 100).clip(lower=-100, upper=100)

    # bb_lower_pct: How far current price is above lower band (as percentage)
    df['bb_lower_pct'] = ((df['close'] - bb_lower) / bb_lower * 100).clip(lower=-100, upper=100)

    # bb_width_pct: Band width as percentage of middle line
    df['bb_width_pct'] = ((bb_upper - bb_lower) / sma * 100).clip(lower=0, upper=100)

    # Remove NaN values
    df = df.dropna()

    # Debug logging for technical indicator statistics
    print(f"Technical indicator stats:", file=sys.stderr)
    print(f"  RSI14 range: {df['rsi14'].min():.3f} - {df['rsi14'].max():.3f}", file=sys.stderr)
    print(f"  BB Upper %: {df['bb_upper_pct'].min():.1f} - {df['bb_upper_pct'].max():.1f}", file=sys.stderr)
    print(f"  BB Lower %: {df['bb_lower_pct'].min():.1f} - {df['bb_lower_pct'].max():.1f}", file=sys.stderr)
    print(f"  BB Width %: {df['bb_width_pct'].min():.1f} - {df['bb_width_pct'].max():.1f}", file=sys.stderr)

    # Return DataFrame with exact column order required
    feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    return df[feature_cols]


def get_random_target_date():
    """Generate random target date between 2024-09-01 and 2025-05-15"""
    start_date = date(2024, 9, 1)
    end_date = date(2025, 5, 15)

    # Calculate the number of days between start and end
    days_between = (end_date - start_date).days

    # Generate random number of days to add to start date
    random_days = random.randint(0, days_between)

    # Return the random date
    return start_date + timedelta(days=random_days)


def load_sp500_tickers():
    """Load S&P 500 tickers from sp500_enriched_final.ts"""
    ROOT_DIR = Path(__file__).resolve().parents[1]
    ts_file = ROOT_DIR / "data" / "sp500_enriched_final.ts"

    if not ts_file.exists():
        print(f"Error: {ts_file} not found", file=sys.stderr)
        sys.exit(1)

    tickers = []
    company_names = {}

    try:
        with open(ts_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Extract ticker symbols and company names using regex
        pattern = r'"([A-Z]+)":\s*{\s*name:\s*"([^"]+)"'
        matches = re.findall(pattern, content)

        for ticker, name in matches:
            tickers.append(ticker)
            company_names[ticker] = name

        return tickers, company_names

    except Exception as e:
        print(f"Error loading tickers: {e}", file=sys.stderr)
        sys.exit(1)


def get_available_dates():
    """Load available dates from the CSV file"""
    ROOT_DIR = Path(__file__).resolve().parents[1]
    csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

    if not csv_path.exists():
        print(f"Error: {csv_path} not found", file=sys.stderr)
        sys.exit(1)

    try:
        import pandas as pd
        df = pd.read_csv(csv_path)
        df['Date'] = pd.to_datetime(df['Date']).dt.date

        # Filter dates between September 2024 and May 15, 2025
        start_date = date(2024, 9, 1)
        end_date = date(2025, 5, 15)

        available_dates = df[(df['Date'] >= start_date) & (df['Date'] <= end_date)]['Date'].tolist()
        return available_dates

    except Exception as e:
        print(f"Error loading available dates: {e}", file=sys.stderr)
        sys.exit(1)


def get_random_date_and_ticker(count=1):
    """Generate random dates and tickers for fine-tuning"""
    # Load all available tickers
    all_tickers, company_names = load_sp500_tickers()

    # Exclude specified tickers
    excluded_tickers = {'SW', 'GEV', 'SOLV', 'VLTO', 'KVUE', 'GEHC', 'CEG'}
    available_tickers = [t for t in all_tickers if t not in excluded_tickers]

    # Load available dates from the dataset
    available_dates = get_available_dates()

    if not available_dates:
        print("No available dates found in the specified range", file=sys.stderr)
        sys.exit(1)

    # Generate random selections
    selections = []
    for _ in range(count):
        # Random date from available dates
        random_date = random.choice(available_dates)

        # Random ticker
        random_ticker = random.choice(available_tickers)

        selections.append({
            'ticker': random_ticker,
            'company_name': company_names[random_ticker],
            'date': random_date
        })

    return selections


def load_and_prepare_data(ticker, target_date, use_volume=False):
    """Load data and prepare for training with flexible date selection"""
    try:
        # Load data
        ROOT_DIR = Path(__file__).resolve().parents[1]
        csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

        if not csv_path.exists():
            print(f"Data file not found: {csv_path}", file=sys.stderr)
            sys.exit(1)

        # Read price data with proper data types
        df = pd.read_csv(csv_path)
        df['Date'] = pd.to_datetime(df['Date']).dt.date

        # Convert numeric columns to float64
        for col in df.columns:
            if col != 'Date':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Check if ticker exists
        if ticker not in df.columns:
            print(f"Ticker {ticker} not found in data", file=sys.stderr)
            sys.exit(1)

        # Calculate training end date (2 days before target date to match lstm_service.py logic)
        train_end_date = target_date - timedelta(days=2)  # 2 days before target
        train_start_date = target_date - timedelta(days=int(3.5 * 365))  # 3.5 years before

        # Filter training data
        train_data = df[(df['Date'] >= train_start_date) & (df['Date'] <= train_end_date)].copy()
        
        if len(train_data) < 100:  # Minimum data requirement
            print(f"Insufficient training data for {ticker} at {target_date}", file=sys.stderr)
            sys.exit(1)

        # Load volume data if requested
        volume_data = None
        if use_volume:
            volume_path = ROOT_DIR / "data" / "sp500_volume_3y.csv"
            if volume_path.exists():
                volume_df = pd.read_csv(volume_path)
                volume_df['Date'] = pd.to_datetime(volume_df['Date']).dt.date

                # Convert numeric columns to float64
                for col in volume_df.columns:
                    if col != 'Date':
                        volume_df[col] = pd.to_numeric(volume_df[col], errors='coerce')

                if ticker in volume_df.columns:
                    volume_data = volume_df[['Date', ticker]].copy()
                    volume_data.columns = ['Date', 'Volume']

        # Get all data for predictions (including target date)
        all_data = df[['Date', ticker]].copy()
        all_data.columns = ['Date', 'Adj Close']
        all_data = all_data.dropna().sort_values('Date')

        # Merge volume data if available
        if use_volume and volume_data is not None:
            volume_data = volume_data.dropna().sort_values('Date')
            all_data = pd.merge(all_data, volume_data, on='Date', how='inner')

            # Also prepare training data with volume
            train_volume = volume_data[
                (volume_data['Date'] >= train_start_date) & 
                (volume_data['Date'] <= train_end_date)
            ].copy()
            train_data_with_volume = pd.merge(
                train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}),
                train_volume,
                on='Date',
                how='inner'
            )
            return all_data, train_data_with_volume, train_end_date
        else:
            return all_data, train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}), train_end_date

    except Exception as e:
        print(f"Error loading data: {e}", file=sys.stderr)
        sys.exit(1)


def create_sequences(df, feature_cols=None):
    """
    Create sequences for LSTM training with technical indicators

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with technical indicator features
    feature_cols : list, optional
        List of feature column names. If None, uses all columns except 'Date'

    Returns:
    --------
    tuple
        (X, y) where X has shape (samples, WINDOW_DAYS, num_features) and y is binary labels
    """
    if feature_cols is None:
        feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']

    X, y = [], []

    # Iterate from 0 to (len(df) - WINDOW_DAYS - HORIZON_DAYS)
    for i in range(len(df) - WINDOW_DAYS - HORIZON_DAYS):
        # Extract sequence from df.iloc[i:i+WINDOW_DAYS, feature_cols] with shape (75, 6)
        sequence = df.iloc[i:i+WINDOW_DAYS][feature_cols].values
        X.append(sequence)

        # Calculate target index: t = i + WINDOW_DAYS - 1, target_date = t + HORIZON_DAYS
        t = i + WINDOW_DAYS - 1
        target_date = t + HORIZON_DAYS

        # Create binary label: 1 if close[target_date] > close[t], else 0
        current_price = df.iloc[t][feature_cols[0]]  # Use first feature column (close)
        future_price = df.iloc[target_date][feature_cols[0]]  # Use first feature column (close)
        y.append(1 if future_price > current_price else 0)

    return np.array(X), np.array(y)


def focal_loss(gamma=2.0):
    """Focal Loss to address class imbalance more effectively than class weights"""
    def loss(y_true, y_pred):
        bce = tf.keras.losses.binary_crossentropy(y_true, y_pred)
        p_t = y_true * y_pred + (1 - y_true) * (1 - y_pred)
        return tf.pow(1 - p_t, gamma) * bce
    return loss


def build_lstm_model(input_shape=(WINDOW_DAYS, 6)):
    """
    Build Intel-optimized LSTM classifier model with L2 regularization

    Parameters:
    -----------
    input_shape : tuple
        Input shape for LSTM, default (75, 6) for technical indicators

    Returns:
    --------
    keras.Model
        Compiled LSTM model
    """
    model = Sequential([
        LSTM(64,
             return_sequences=True,
             input_shape=input_shape,
             kernel_regularizer=l2(1e-4),
             recurrent_regularizer=l2(1e-4)),
        LSTM(64,
             return_sequences=False,
             kernel_regularizer=l2(1e-4),
             recurrent_regularizer=l2(1e-4)),
        Dropout(0.2),  # Increased dropout for 6 features

        Dense(128,
              activation='relu',
              kernel_regularizer=l2(1e-4)),

        Dense(1, activation='sigmoid',
              kernel_regularizer=l2(1e-4))
    ])

    model.compile(
        optimizer=AdamW(learning_rate=0.0008, weight_decay=1e-4),
        loss=focal_loss(gamma=2.0),
        metrics=['binary_accuracy']
    )

    return model


def get_calibrated_probability(model, iso_calibrator, sequence):
    """Get calibrated probability using Isotonic Regression with proper input type"""
    raw_prediction = model.predict(sequence, verbose=0)[0][0]
    calibrated_prob = iso_calibrator.predict(np.array([raw_prediction]))[0]
    return calibrated_prob


def update_traffic_light_history(ticker, is_correct, target_date):
    """
    Update traffic light performance history for a ticker

    Parameters:
    -----------
    ticker : str
        Stock ticker symbol
    is_correct : int
        1 if prediction was correct, 0 if incorrect
    target_date : date
        Date of the prediction

    Returns:
    --------
    str
        Traffic light color: 'green', 'yellow', or 'red'
    """
    # Create traffic_history directory if it doesn't exist
    ROOT_DIR = Path(__file__).resolve().parents[1]
    history_dir = ROOT_DIR / "data" / "traffic_history"
    history_dir.mkdir(parents=True, exist_ok=True)

    # Path to ticker's history file
    history_file = history_dir / f"{ticker}.csv"

    # Load existing history or create new DataFrame
    if history_file.exists():
        try:
            history_df = pd.read_csv(history_file)
            history_df['date'] = pd.to_datetime(history_df['date']).dt.date
        except Exception as e:
            print(f"Error reading history file {history_file}: {e}", file=sys.stderr)
            history_df = pd.DataFrame(columns=['date', 'is_correct'])
    else:
        history_df = pd.DataFrame(columns=['date', 'is_correct'])

    # Add new record
    new_record = pd.DataFrame({
        'date': [target_date],
        'is_correct': [is_correct]
    })
    history_df = pd.concat([history_df, new_record], ignore_index=True)

    # Keep only last TL_WINDOW (5) rows
    history_df = history_df.tail(TL_WINDOW)

    # Save updated history
    history_df.to_csv(history_file, index=False)

    # Calculate score and determine traffic light
    score = history_df['is_correct'].sum()

    if len(history_df) < TL_WINDOW:
        # Default to yellow if history length < TL_WINDOW
        traffic_light = "yellow"
    elif score >= 4:
        # 4-5 correct predictions = green
        traffic_light = "green"
    elif score >= 2:
        # 2-3 correct predictions = yellow
        traffic_light = "yellow"
    else:
        # 0-1 correct predictions = red
        traffic_light = "red"

    print(f"Traffic-light score: {score} – {traffic_light}", file=sys.stderr)

    return traffic_light


def predict_for_dates(model, scaler, all_data, target_date, ticker, iso_calibrator=None, best_threshold=0.5):
    """
    Make predictions for target date using technical indicators

    Parameters:
    -----------
    model : keras.Model
        Trained LSTM model
    scaler : sklearn.preprocessing.MinMaxScaler
        Fitted scaler for features
    all_data : pandas.DataFrame
        DataFrame with technical indicators
    target_date : date
        Target prediction date
    ticker : str
        Stock ticker symbol
    iso_calibrator : sklearn.isotonic.IsotonicRegression, optional
        Calibrator for probability adjustment
    best_threshold : float
        Optimal threshold for binary classification

    Returns:
    --------
    dict
        Prediction result with traffic light
    """
    # Build technical indicators on input data
    indicators_df = compute_indicators(all_data)

    # Find target date in data
    date_mask = all_data['Date'] == target_date
    if not date_mask.any():
        # Find nearest available date before target
        available_dates = all_data['Date'].values
        dates_before = [d for d in available_dates if d <= target_date]
        if not dates_before:
            print(f"No data available before {target_date}", file=sys.stderr)
            sys.exit(1)
        actual_date = max(dates_before)
        date_mask = all_data['Date'] == actual_date
        print(f"Using nearest available date: {actual_date}", file=sys.stderr)
    else:
        actual_date = target_date

    date_idx = all_data[date_mask].index[0]

    # Check if we have enough data for WINDOW_DAYS sequence
    if date_idx < WINDOW_DAYS:
        print(f"Insufficient data before {actual_date}", file=sys.stderr)
        sys.exit(1)

    # Check if we have enough future data for HORIZON_DAYS prediction
    if date_idx + HORIZON_DAYS - 1 >= len(all_data):
        print(f"Insufficient future data for {HORIZON_DAYS}-day prediction at {actual_date}", file=sys.stderr)
        sys.exit(1)

    # Extract most recent WINDOW_DAYS (75) days and apply saved scaler
    sequence_data = indicators_df.iloc[date_idx-WINDOW_DAYS:date_idx].values
    sequence_scaled = scaler.transform(sequence_data)
    sequence_scaled = sequence_scaled.reshape(1, WINDOW_DAYS, 6)  # (1, 75, 6)

    # Generate model.predict() output and apply saved calibrator
    if iso_calibrator is not None:
        pred_prob_up = get_calibrated_probability(model, iso_calibrator, sequence_scaled)
    else:
        pred_prob_up = model.predict(sequence_scaled, verbose=0)[0][0]

    pred_prob_down = 1.0 - pred_prob_up

    # Use saved ROC threshold for binary classification
    predicted_label = int(pred_prob_up > best_threshold)

    # Calculate actual_label from target_date if data available
    price_col = 'Adj Close' if 'Adj Close' in all_data.columns else 'close'
    current_price = all_data.iloc[date_idx-1][price_col]
    future_price = all_data.iloc[date_idx + HORIZON_DAYS - 1][price_col]
    actual_label = 1 if future_price > current_price else 0

    # Calculate is_correct and update traffic light history
    is_correct = int(predicted_label == actual_label)
    traffic_light = update_traffic_light_history(ticker, is_correct, actual_date)

    return {
        "date": actual_date.strftime("%Y-%m-%d"),
        "pred_prob_up": round(float(pred_prob_up), 4),
        "pred_prob_down": round(float(pred_prob_down), 4),
        "predicted_label": int(predicted_label),
        "actual_label": int(actual_label),
        "prediction_horizon": HORIZON_DAYS,
        "optimal_threshold": round(float(best_threshold), 4),
        "traffic_light": traffic_light
    }


def determine_result_color(predictions):
    """Determine result color based on prediction accuracy (matching lstm_service.py)"""
    correct_count = sum(1 for p in predictions if p["predicted_label"] == p["actual_label"])

    if correct_count == 2:
        return "green"
    elif correct_count == 1:
        return "yellow"
    else:
        return "red"


def save_result_to_file(result, company_name=None, target_date=None, output_dir=None):
    """Save LSTM result to JSON file with proper naming convention"""
    # Set default output directory
    if output_dir is None:
        ROOT_DIR = Path(__file__).resolve().parents[1]
        output_dir = ROOT_DIR / "data" / "finetuning"

    # Ensure output directory exists
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Create filename based on company name and date
    if company_name and target_date:
        # Clean company name for filename
        clean_name = re.sub(r'[^\w\s-]', '', company_name).strip()
        clean_name = re.sub(r'[-\s]+', '_', clean_name)
        if isinstance(target_date, date):
            target_date_str = target_date.strftime('%Y-%m-%d')
        else:
            target_date_str = str(target_date)
        filename = f"{clean_name}_{target_date_str}.json"
    else:
        # Fallback to timestamp-based naming
        ts = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"lstm_result_{ts}.json"

    # Save to file
    file_path = output_dir / filename
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    return file_path


def process_single_ticker(ticker, target_date, use_volume=True, save_to_file=False, company_name=None):
    """Process a single ticker and return the result with technical indicators"""
    try:
        # Load and prepare data
        all_data, train_data, train_end_date = load_and_prepare_data(ticker, target_date, use_volume)

        # Compute technical indicators on training data
        train_indicators = compute_indicators(train_data)

        print(f"Input shape: {train_indicators.shape}", file=sys.stderr)

        # Scale the features
        scaler = MinMaxScaler(feature_range=(0, 1))
        train_scaled = scaler.fit_transform(train_indicators.values)

        # Create sequences with technical indicators and HORIZON_DAYS prediction
        X_train, y_train = create_sequences(pd.DataFrame(train_scaled, columns=train_indicators.columns))

        print(f"Input shape: {X_train.shape}", file=sys.stderr)  # Should show (samples, 75, 6)
        print(f"Label balance: {y_train.mean():.3f}", file=sys.stderr)  # Should be ~0.5 ± 0.1

        # Build model for 6 features (close, volume, rsi14, bb_upper_pct, bb_lower_pct, bb_width_pct)
        model = build_lstm_model((WINDOW_DAYS, 6))

        # Manual time series validation split (preserve order)
        split_idx = int(len(X_train) * 0.9)
        X_tr, X_val = X_train[:split_idx], X_train[split_idx:]
        y_tr, y_val = y_train[:split_idx], y_train[split_idx:]

        # Calculate class weights to address imbalance
        class_weights = compute_class_weight('balanced', classes=np.array([0, 1]), y=y_tr)
        class_weight_dict = {0: class_weights[0], 1: class_weights[1]}

        # Early stopping callback
        early_stopping = EarlyStopping(
            monitor='val_binary_accuracy',
            patience=5,
            restore_best_weights=True
        )

        # Train with class weights and manual validation split
        model.fit(
            X_tr, y_tr,
            epochs=10,
            batch_size=256,
            validation_data=(X_val, y_val),
            callbacks=[early_stopping],
            class_weight=class_weight_dict,
            shuffle=False,  # Preserve time series order
            verbose=0
        )

        # Train Isotonic Regression for better calibration
        raw_val_predictions = model.predict(X_val, verbose=0).ravel()

        # Sort validation data by raw predictions for proper isotonic fitting
        sort_idx = np.argsort(raw_val_predictions)
        sorted_raw_preds = raw_val_predictions[sort_idx]
        sorted_y_val = y_val[sort_idx]

        # Train isotonic calibrator with sorted data
        iso_calibrator = IsotonicRegression(out_of_bounds='clip')
        iso_calibrator.fit(sorted_raw_preds, sorted_y_val)

        # Get calibrated validation predictions for threshold calculation
        calibrated_val_preds = iso_calibrator.predict(sorted_raw_preds)

        # Calculate optimal threshold using calibrated scores
        fpr, tpr, thresholds = roc_curve(sorted_y_val, calibrated_val_preds)
        best_threshold = thresholds[(tpr - fpr).argmax()]

        # Save model and calibrators with version tags
        ROOT_DIR = Path(__file__).resolve().parents[1]
        model_dir = ROOT_DIR / "data" / "lstm_results"
        model_dir.mkdir(parents=True, exist_ok=True)

        # File naming with version tags: w75_h5_rsi_bb
        version_tag = "w75_h5_rsi_bb"
        model.save(model_dir / f"{ticker}_model_{version_tag}.keras")
        joblib.dump(scaler, model_dir / f"{ticker}_scaler_{version_tag}.pkl")
        joblib.dump(iso_calibrator, model_dir / f"{ticker}_cal_{version_tag}.pkl")
        joblib.dump(best_threshold, model_dir / f"{ticker}_thr_{version_tag}.pkl")

        # Make prediction for target date with technical indicators
        prediction = predict_for_dates(model, scaler, all_data, target_date, ticker, iso_calibrator=iso_calibrator, best_threshold=best_threshold)

        # Create result with traffic light
        result = {
            "symbol": ticker,
            "train_until": train_end_date.strftime("%Y-%m-%d"),
            "target_date": target_date.strftime("%Y-%m-%d"),
            "prediction": prediction,
            "traffic_light": prediction["traffic_light"]
        }

        # Save to file if requested
        if save_to_file:
            file_path = save_result_to_file(result, company_name, target_date)
            print(f"✔ {ticker} → {file_path.name} saved", file=sys.stderr)

        return result

    except Exception as e:
        print(f"Error processing {ticker}: {e}", file=sys.stderr)
        return None


def main():
    """Main function for LSTM fine-tuning service with batch processing support"""
    parser = argparse.ArgumentParser(
        description="LSTM Fine-tuning Service with Random Ticker and Date Selection",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python lstm_finetuning.py --count 10           # Generate 10 random samples
  python lstm_finetuning.py AAPL 2024-12-15      # Specific ticker and date
  python lstm_finetuning.py AAPL --random        # Specific ticker, random date
  python lstm_finetuning.py AAPL                 # Specific ticker, random date
        """
    )

    parser.add_argument(
        '--count', '-c',
        type=int,
        help='Number of random fine-tuning samples to generate'
    )

    parser.add_argument(
        'ticker',
        nargs='?',
        help='Ticker symbol (required unless using --count)'
    )

    parser.add_argument(
        'date',
        nargs='?',
        help='Target date (YYYY-MM-DD) or --random'
    )

    parser.add_argument(
        '--no-volume',
        action='store_true',
        help='Disable volume features (use price only)'
    )

    args = parser.parse_args()

    # Enable volume by default, disable if --no-volume flag is used
    use_volume = not args.no_volume

    if args.count:
        # Batch processing mode: generate multiple random samples
        print(f"[INFO] Generating {args.count} random LSTM fine-tuning samples...", file=sys.stderr)
        selections = get_random_date_and_ticker(args.count)

        success_count = 0
        for i, selection in enumerate(selections, 1):
            try:
                ticker = selection['ticker']
                company_name = selection['company_name']
                target_date = selection['date']

                print(f"[INFO] Processing {i}/{args.count}: {ticker} ({company_name}) on {target_date.strftime('%Y-%m-%d')}", file=sys.stderr)

                result = process_single_ticker(ticker, target_date, use_volume, save_to_file=True, company_name=company_name)
                if result:
                    success_count += 1

            except Exception as e:
                print(f"[ERROR] {selection['ticker']}: {e}", file=sys.stderr)

        print(f"[INFO] Completed! Successfully processed {success_count}/{args.count} samples", file=sys.stderr)

    else:
        # Single ticker mode
        if not args.ticker:
            parser.error("Ticker is required unless using --count mode")

        ticker = args.ticker.upper()

        # Determine target date
        if args.date:
            if args.date == '--random':
                target_date = get_random_target_date()
                print(f"Using random target date: {target_date}", file=sys.stderr)
            else:
                try:
                    target_date = date.fromisoformat(args.date)
                except ValueError:
                    print(f"Invalid date format: {args.date}. Use YYYY-MM-DD or --random", file=sys.stderr)
                    sys.exit(1)
        else:
            # Default to random date if no date specified
            target_date = get_random_target_date()
            print(f"Using random target date: {target_date}", file=sys.stderr)

        # Process single ticker and output to stdout
        result = process_single_ticker(ticker, target_date, use_volume, save_to_file=False)
        if result:
            print(json.dumps(result))
        else:
            sys.exit(1)


if __name__ == "__main__":
    main()
