#!/usr/bin/env python3
"""
Intel-Optimized LSTM Stock Prediction Service for Fine-tuning
6-feature input (adj close, volume, RSI-14, <PERSON><PERSON>er %) with 75-day window / 5-day horizon
Trains up to day -5, predicts 6 days (-4, -3, -2, -1, 0, +1) with bias offset only
"""

import os
import sys
import json
import random
import re
import argparse
from datetime import date, timedelta, datetime
from pathlib import Path

# Intel optimization environment variables
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['KMP_AFFINITY'] = 'granularity=fine,compact,1,0'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
os.environ['TF_ENABLE_BF16_CONVOLUTIONS'] = '1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['PYTHONWARNINGS'] = 'ignore'

# Suppress warnings
import warnings
warnings.filterwarnings('ignore')

# TensorFlow imports with Intel optimizations
try:
    import tensorflow as tf
    tf.get_logger().setLevel('ERROR')

    # Enable Intel optimizations
    tf.config.threading.set_intra_op_parallelism_threads(16)
    tf.config.threading.set_inter_op_parallelism_threads(16)

    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.callbacks import EarlyStopping

    print("✅  TensorFlow-Intel 2.18.0 — oneDNN enabled", file=sys.stderr)

except ImportError as e:
    print(f"TensorFlow import error: {e}", file=sys.stderr)
    sys.exit(1)

import numpy as np
import pandas as pd
from pandas.tseries.offsets import BDay
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import joblib

# Common Constants for Technical Indicator-based LSTM Prediction
WINDOW_DAYS = 75        # LSTM input sequence length (75 trading days)
HORIZON_DAYS = 5        # Prediction horizon (5 business days ahead)
RSI_PERIOD = 14         # RSI calculation period
BB_PERIOD = 20          # Bollinger Bands period
BB_STD_MULT = 2         # Bollinger Bands standard deviation multiplier


def compute_indicators(df):
    """
    Compute technical indicators for LSTM features

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with columns ['Date', 'close', 'volume'] (or similar)

    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    """
    # Ensure we have the required columns
    if 'Adj Close' in df.columns:
        df = df.rename(columns={'Adj Close': 'close'})
    if 'Volume' in df.columns:
        df = df.rename(columns={'Volume': 'volume'})

    # Sort by date to ensure proper calculation
    df = df.sort_values('Date').copy()

    # Calculate RSI(14)
    delta = df['close'].diff()
    gain = delta.clip(lower=0)
    loss = -delta.clip(upper=0)

    # Use exponential moving average for RSI calculation
    avg_gain = gain.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    avg_loss = loss.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    # Normalize RSI from 0-100 range to 0-1 scale
    df['rsi14'] = rsi / 100.0

    # Calculate Bollinger Bands
    sma = df['close'].rolling(window=BB_PERIOD).mean()
    std = df['close'].rolling(window=BB_PERIOD).std()
    bb_upper = sma + (BB_STD_MULT * std)
    bb_lower = sma - (BB_STD_MULT * std)

    # Calculate Bollinger Band features as percentages
    # bb_upper_pct: How far current price is above upper band (as percentage)
    df['bb_upper_pct'] = ((df['close'] - bb_upper) / bb_upper * 100).clip(lower=-100, upper=100)

    # bb_lower_pct: How far current price is above lower band (as percentage)
    df['bb_lower_pct'] = ((df['close'] - bb_lower) / bb_lower * 100).clip(lower=-100, upper=100)

    # bb_width_pct: Band width as percentage of middle line
    df['bb_width_pct'] = ((bb_upper - bb_lower) / sma * 100).clip(lower=0, upper=100)

    # Remove NaN values
    df = df.dropna()

    # Debug logging for technical indicator statistics
    print(f"Technical indicator stats:", file=sys.stderr)
    print(f"  RSI14 range: {df['rsi14'].min():.3f} - {df['rsi14'].max():.3f}", file=sys.stderr)
    print(f"  BB Upper %: {df['bb_upper_pct'].min():.1f} - {df['bb_upper_pct'].max():.1f}", file=sys.stderr)
    print(f"  BB Lower %: {df['bb_lower_pct'].min():.1f} - {df['bb_lower_pct'].max():.1f}", file=sys.stderr)
    print(f"  BB Width %: {df['bb_width_pct'].min():.1f} - {df['bb_width_pct'].max():.1f}", file=sys.stderr)

    # Return DataFrame with exact column order required
    feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    return df[feature_cols]


def get_random_target_date():
    """Generate random target date between 2024-09-01 and 2025-05-15"""
    start_date = date(2024, 9, 1)
    end_date = date(2025, 5, 15)

    # Calculate the number of days between start and end
    days_between = (end_date - start_date).days

    # Generate random number of days to add to start date
    random_days = random.randint(0, days_between)

    # Return the random date
    return start_date + timedelta(days=random_days)


def load_sp500_tickers():
    """Load S&P 500 tickers from sp500_enriched_final.ts"""
    ROOT_DIR = Path(__file__).resolve().parents[1]
    ts_file = ROOT_DIR / "data" / "sp500_enriched_final.ts"

    if not ts_file.exists():
        print(f"Error: {ts_file} not found", file=sys.stderr)
        sys.exit(1)

    tickers = []
    company_names = {}

    try:
        with open(ts_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Extract ticker symbols and company names using regex
        pattern = r'"([A-Z]+)":\s*{\s*name:\s*"([^"]+)"'
        matches = re.findall(pattern, content)

        for ticker, name in matches:
            tickers.append(ticker)
            company_names[ticker] = name

        return tickers, company_names

    except Exception as e:
        print(f"Error loading tickers: {e}", file=sys.stderr)
        sys.exit(1)


def get_available_dates():
    """Load available dates from the CSV file"""
    ROOT_DIR = Path(__file__).resolve().parents[1]
    csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

    if not csv_path.exists():
        print(f"Error: {csv_path} not found", file=sys.stderr)
        sys.exit(1)

    try:
        import pandas as pd
        df = pd.read_csv(csv_path)
        df['Date'] = pd.to_datetime(df['Date']).dt.date

        # Filter dates between September 2024 and May 15, 2025
        start_date = date(2024, 9, 1)
        end_date = date(2025, 5, 15)

        available_dates = df[(df['Date'] >= start_date) & (df['Date'] <= end_date)]['Date'].tolist()
        return available_dates

    except Exception as e:
        print(f"Error loading available dates: {e}", file=sys.stderr)
        sys.exit(1)


def get_random_date_and_ticker(count=1):
    """Generate random dates and tickers for fine-tuning"""
    # Load all available tickers
    all_tickers, company_names = load_sp500_tickers()

    # Exclude specified tickers
    excluded_tickers = {'SW', 'GEV', 'SOLV', 'VLTO', 'KVUE', 'GEHC', 'CEG'}
    available_tickers = [t for t in all_tickers if t not in excluded_tickers]

    # Load available dates from the dataset
    available_dates = get_available_dates()

    if not available_dates:
        print("No available dates found in the specified range", file=sys.stderr)
        sys.exit(1)

    # Generate random selections
    selections = []
    for _ in range(count):
        # Random date from available dates
        random_date = random.choice(available_dates)

        # Random ticker
        random_ticker = random.choice(available_tickers)

        selections.append({
            'ticker': random_ticker,
            'company_name': company_names[random_ticker],
            'date': random_date
        })

    return selections


def load_and_prepare_data(ticker, target_date, use_volume=False):
    """Load data and prepare for training with flexible date selection"""
    try:
        # Load data
        ROOT_DIR = Path(__file__).resolve().parents[1]
        csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

        if not csv_path.exists():
            print(f"Data file not found: {csv_path}", file=sys.stderr)
            sys.exit(1)

        # Read price data with proper data types
        df = pd.read_csv(csv_path)
        df['Date'] = pd.to_datetime(df['Date']).dt.date

        # Convert numeric columns to float64
        for col in df.columns:
            if col != 'Date':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Check if ticker exists
        if ticker not in df.columns:
            print(f"Ticker {ticker} not found in data", file=sys.stderr)
            sys.exit(1)

        # Calculate training end date (2 days before target date to match lstm_service.py logic)
        train_end_date = target_date - timedelta(days=2)  # 2 days before target
        train_start_date = target_date - timedelta(days=int(3.5 * 365))  # 3.5 years before

        # Filter training data
        train_data = df[(df['Date'] >= train_start_date) & (df['Date'] <= train_end_date)].copy()
        
        if len(train_data) < 100:  # Minimum data requirement
            print(f"Insufficient training data for {ticker} at {target_date}", file=sys.stderr)
            sys.exit(1)

        # Load volume data if requested
        volume_data = None
        if use_volume:
            volume_path = ROOT_DIR / "data" / "sp500_volume_3y.csv"
            if volume_path.exists():
                volume_df = pd.read_csv(volume_path)
                volume_df['Date'] = pd.to_datetime(volume_df['Date']).dt.date

                # Convert numeric columns to float64
                for col in volume_df.columns:
                    if col != 'Date':
                        volume_df[col] = pd.to_numeric(volume_df[col], errors='coerce')

                if ticker in volume_df.columns:
                    volume_data = volume_df[['Date', ticker]].copy()
                    volume_data.columns = ['Date', 'Volume']

        # Get all data for predictions (including target date)
        all_data = df[['Date', ticker]].copy()
        all_data.columns = ['Date', 'Adj Close']
        all_data = all_data.dropna().sort_values('Date')

        # Merge volume data if available
        if use_volume and volume_data is not None:
            volume_data = volume_data.dropna().sort_values('Date')
            all_data = pd.merge(all_data, volume_data, on='Date', how='inner')

            # Also prepare training data with volume
            train_volume = volume_data[
                (volume_data['Date'] >= train_start_date) & 
                (volume_data['Date'] <= train_end_date)
            ].copy()
            train_data_with_volume = pd.merge(
                train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}),
                train_volume,
                on='Date',
                how='inner'
            )
            return all_data, train_data_with_volume, train_end_date
        else:
            return all_data, train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}), train_end_date

    except Exception as e:
        print(f"Error loading data: {e}", file=sys.stderr)
        sys.exit(1)


def create_sequences(df, feature_cols=None):
    """
    Create sequences for LSTM training with technical indicators

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with technical indicator features
    feature_cols : list, optional
        List of feature column names. If None, uses all columns except 'Date'

    Returns:
    --------
    tuple
        (X, y) where X has shape (samples, WINDOW_DAYS, num_features) and y is binary labels
    """
    if feature_cols is None:
        feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']

    X, y = [], []

    # Iterate from 0 to (len(df) - WINDOW_DAYS - HORIZON_DAYS)
    for i in range(len(df) - WINDOW_DAYS - HORIZON_DAYS):
        # Extract sequence from df.iloc[i:i+WINDOW_DAYS, feature_cols] with shape (75, 6)
        sequence = df.iloc[i:i+WINDOW_DAYS][feature_cols].values
        X.append(sequence)

        # Calculate target index: t = i + WINDOW_DAYS - 1, target_date = t + HORIZON_DAYS
        t = i + WINDOW_DAYS - 1
        target_date = t + HORIZON_DAYS

        # Create binary label: 1 if close[target_date] > close[t], else 0
        current_price = df.iloc[t][feature_cols[0]]  # Use first feature column (close)
        future_price = df.iloc[target_date][feature_cols[0]]  # Use first feature column (close)
        y.append(1 if future_price > current_price else 0)

    return np.array(X), np.array(y)


def focal_loss(gamma=2.0):
    """Focal Loss to address class imbalance (no class weights)"""
    def loss(y_true, y_pred):
        bce = tf.keras.losses.binary_crossentropy(y_true, y_pred)
        p_t = y_true * y_pred + (1 - y_true) * (1 - y_pred)
        return tf.pow(1 - p_t, gamma) * bce
    return loss


def build_lstm_model(input_shape=(WINDOW_DAYS, 6)):
    """
    Build simplified LSTM classifier model

    Architecture: LSTM(32) → Dropout(0.2) → LSTM(32) → Dense(1, sigmoid)
    Loss: focal_loss(gamma=2.0) with no class_weight
    """
    model = Sequential([
        LSTM(32, return_sequences=True, input_shape=input_shape),
        Dropout(0.2),
        LSTM(32, return_sequences=False),
        Dense(1, activation='sigmoid')
    ])

    model.compile(
        optimizer='adam',
        loss=focal_loss(gamma=2.0),
        metrics=['binary_accuracy']
    )

    return model


def update_history_csv(ticker, prediction_date, is_correct):
    """
    Update traffic light history CSV (keep last 5 rows)

    Parameters:
    -----------
    ticker : str
        Stock ticker symbol
    prediction_date : date
        Date of the prediction
    is_correct : int
        1 if prediction was correct, 0 if incorrect
    """
    # Create traffic_history directory if it doesn't exist
    ROOT_DIR = Path(__file__).resolve().parents[1]
    history_dir = ROOT_DIR / "data" / "traffic_history"
    history_dir.mkdir(parents=True, exist_ok=True)

    # Path to ticker's history file
    history_file = history_dir / f"{ticker}.csv"

    # Load existing history or create new DataFrame
    if history_file.exists():
        try:
            history_df = pd.read_csv(history_file)
            history_df['date'] = pd.to_datetime(history_df['date']).dt.date
        except Exception as e:
            print(f"Error reading history file {history_file}: {e}", file=sys.stderr)
            history_df = pd.DataFrame(columns=['date', 'is_correct'])
    else:
        history_df = pd.DataFrame(columns=['date', 'is_correct'])

    # Add new record
    new_record = pd.DataFrame({
        'date': [prediction_date],
        'is_correct': [is_correct]
    })
    history_df = pd.concat([history_df, new_record], ignore_index=True)

    # Keep only last 5 rows
    history_df = history_df.tail(5)

    # Save updated history
    history_df.to_csv(history_file, index=False)


def build_sequence(all_data, indicators_df, scaler, target_date):
    """
    Build a single sequence for prediction on target_date

    Parameters:
    -----------
    all_data : pandas.DataFrame
        DataFrame with Date and price columns
    indicators_df : pandas.DataFrame
        DataFrame with technical indicators
    scaler : sklearn.preprocessing.MinMaxScaler
        Fitted scaler for features
    target_date : date
        Target date for sequence building

    Returns:
    --------
    numpy.ndarray
        Scaled sequence of shape (1, 75, 6)
    """
    # Find target date in data
    date_mask = all_data['Date'] == target_date
    if not date_mask.any():
        # Find nearest available date before target
        available_dates = all_data['Date'].values
        dates_before = [d for d in available_dates if d <= target_date]
        if not dates_before:
            raise ValueError(f"No data available before {target_date}")
        actual_date = max(dates_before)
        date_mask = all_data['Date'] == actual_date

    date_idx = all_data[date_mask].index[0]

    # Check if we have enough data for WINDOW_DAYS sequence
    if date_idx < WINDOW_DAYS:
        raise ValueError(f"Insufficient data before {target_date}")

    # Extract most recent WINDOW_DAYS (75) days and apply scaler
    sequence_data = indicators_df.iloc[date_idx-WINDOW_DAYS:date_idx].values
    sequence_scaled = scaler.transform(sequence_data)
    return sequence_scaled.reshape(1, WINDOW_DAYS, 6)  # (1, 75, 6)


def get_actual_label_if_available(all_data, target_date):
    """
    Get actual label for target_date if data is available

    Returns None for future dates, 0/1 for historical dates
    """
    try:
        date_mask = all_data['Date'] == target_date
        if not date_mask.any():
            return None

        date_idx = all_data[date_mask].index[0]

        # Check if we have future data for HORIZON_DAYS prediction
        if date_idx + HORIZON_DAYS - 1 >= len(all_data):
            return None

        # Calculate actual label
        price_col = 'Adj Close' if 'Adj Close' in all_data.columns else 'close'
        current_price = all_data.iloc[date_idx-1][price_col]
        future_price = all_data.iloc[date_idx + HORIZON_DAYS - 1][price_col]
        return 1 if future_price > current_price else 0

    except Exception:
        return None


def save_result_to_file(result, company_name=None, target_date=None, output_dir=None):
    """Save LSTM result to JSON file with proper naming convention"""
    # Set default output directory
    if output_dir is None:
        ROOT_DIR = Path(__file__).resolve().parents[1]
        output_dir = ROOT_DIR / "data" / "finetuning"

    # Ensure output directory exists
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Create filename based on company name and date
    if company_name and target_date:
        # Clean company name for filename
        clean_name = re.sub(r'[^\w\s-]', '', company_name).strip()
        clean_name = re.sub(r'[-\s]+', '_', clean_name)
        if isinstance(target_date, date):
            target_date_str = target_date.strftime('%Y-%m-%d')
        else:
            target_date_str = str(target_date)
        filename = f"{clean_name}_{target_date_str}.json"
    else:
        # Fallback to timestamp-based naming
        ts = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"lstm_result_{ts}.json"

    # Save to file
    file_path = output_dir / filename
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    return file_path


def process_single_ticker(ticker, target_date, use_volume=True, save_to_file=False, company_name=None):
    """
    Process a single ticker with new 6-day prediction approach

    Train up to day -5, predict 6 days (-4, -3, -2, -1, 0, +1)
    Use bias offset only (no calibration/thresholds)
    """
    try:
        # Load and prepare data
        all_data, train_data, _ = load_and_prepare_data(ticker, target_date, use_volume)

        # Calculate train_until (5 business days before target_date)
        train_until = target_date - BDay(5)

        # Filter training data up to train_until
        train_data_filtered = train_data[train_data['Date'] <= train_until].copy()

        if len(train_data_filtered) < 100:
            print(f"Insufficient training data for {ticker} at {target_date}", file=sys.stderr)
            return None

        # Compute technical indicators on training data
        train_indicators = compute_indicators(train_data_filtered)

        print(f"학습 데이터 형태: {train_indicators.shape}", file=sys.stderr)

        # Scale the features
        scaler = MinMaxScaler(feature_range=(0, 1))
        train_scaled = scaler.fit_transform(train_indicators.values)

        # Create sequences with technical indicators
        X_train, y_train = create_sequences(pd.DataFrame(train_scaled, columns=train_indicators.columns))

        print(f"입력 형태: {X_train.shape}", file=sys.stderr)  # Should show (samples, 75, 6)
        print(f"학습 라벨 비율:", round(y_train.mean(), 3), file=sys.stderr)

        # Calculate bias offset
        bias = float(y_train.mean() - 0.5)
        print(f"편향 오프셋(bias):", round(bias, 3), file=sys.stderr)

        # Build simplified model
        model = build_lstm_model((WINDOW_DAYS, 6))

        # Early stopping callback
        early_stopping = EarlyStopping(
            monitor='val_loss',
            patience=3,
            restore_best_weights=True,
            verbose=2
        )

        # Train model (no class weights, no manual validation split)
        model.fit(
            X_train, y_train,
            epochs=20,
            batch_size=32,
            validation_split=0.15,
            callbacks=[early_stopping],
            shuffle=False,  # Preserve time series order
            verbose=0
        )

        # Save model, scaler, and bias
        ROOT_DIR = Path(__file__).resolve().parents[1]
        model_dir = ROOT_DIR / "data" / "lstm_results"
        model_dir.mkdir(parents=True, exist_ok=True)

        model.save(model_dir / f"{ticker}_model_w75_h5_rsi_bb.keras")
        joblib.dump(scaler, model_dir / f"{ticker}_scaler_w75.pkl")

        # Save bias to JSON
        bias_data = {"bias": bias}
        with open(model_dir / f"{ticker}_bias.json", 'w') as f:
            json.dump(bias_data, f)

        # Compute technical indicators on all data for predictions
        all_indicators = compute_indicators(all_data)

        # Generate 6-day predictions (-4, -3, -2, -1, 0, +1)
        dates_to_predict = (
            pd.bdate_range(end=train_until, periods=HORIZON_DAYS, closed="left")
            .append(pd.bdate_range(start=train_until + BDay(1), periods=1))
        )

        predictions, y_true, y_pred = [], [], []

        for d in dates_to_predict:
            try:
                # Build sequence for prediction
                seq = build_sequence(all_data, all_indicators, scaler, d - BDay(1))

                # Get raw prediction and apply bias offset
                p_raw = float(model.predict(seq, verbose=0)[0][0])
                p_adj = np.clip(p_raw - bias, 0.0, 1.0)
                lbl = int(p_adj > 0.5)

                # Get actual label if available
                act = get_actual_label_if_available(all_data, d)
                if act is not None:
                    y_true.append(act)
                    y_pred.append(lbl)
                    update_history_csv(ticker, d, int(lbl == act))

                predictions.append({
                    "date": d.strftime("%Y-%m-%d"),
                    "pred_prob_up": round(p_adj, 4),
                    "pred_prob_down": round(1 - p_adj, 4),
                    "predicted_label": lbl,
                    "actual_label": act,
                    "prediction_horizon": 5
                })

            except Exception as e:
                print(f"Error predicting for {d}: {e}", file=sys.stderr)
                continue

        # Calculate metrics using first 5 days that have labels
        if y_true:
            metrics = {
                "accuracy": round(accuracy_score(y_true, y_pred), 3),
                "precision": round(precision_score(y_true, y_pred, zero_division=0), 3),
                "recall": round(recall_score(y_true, y_pred, zero_division=0), 3),
                "f1": round(f1_score(y_true, y_pred, zero_division=0), 3)
            }
        else:
            metrics = {k: None for k in ["accuracy", "precision", "recall", "f1"]}

        print(f"성능 지표:", metrics, file=sys.stderr)

        # ── Traffic light thresholds (editable):
        #   prob_up < 0.45            → RED
        #   0.45 ≤ prob_up ≤ 0.55     → YELLOW
        #   prob_up > 0.55            → GREEN
        prob_up_next = predictions[-1]["pred_prob_up"]  # day +1
        if prob_up_next < 0.45:
            tl_color = "red"
        elif prob_up_next > 0.55:
            tl_color = "green"
        else:
            tl_color = "yellow"

        print(f"신호등:", tl_color, file=sys.stderr)

        # Create result
        result = {
            "symbol": ticker,
            "train_until": train_until.strftime("%Y-%m-%d"),
            "predictions": predictions,
            "traffic_light": tl_color,
            "metrics": metrics
        }

        # Save to file if requested
        if save_to_file:
            # Save JSON result with ticker_date format
            ROOT_DIR = Path(__file__).resolve().parents[1]
            output_dir = ROOT_DIR / "data" / "finetuning"
            output_dir.mkdir(parents=True, exist_ok=True)

            filename = f"{ticker}_{train_until.strftime('%Y-%m-%d')}.json"
            file_path = output_dir / filename

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            print(f"✔ {ticker} → {filename} saved", file=sys.stderr)

        return result

    except Exception as e:
        print(f"Error processing {ticker}: {e}", file=sys.stderr)
        return None


def main():
    """Main function for LSTM fine-tuning service with batch processing support"""
    parser = argparse.ArgumentParser(
        description="LSTM Fine-tuning Service with Random Ticker and Date Selection",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python lstm_finetuning.py --count 10           # Generate 10 random samples
  python lstm_finetuning.py AAPL 2024-12-15      # Specific ticker and date
  python lstm_finetuning.py AAPL --random        # Specific ticker, random date
  python lstm_finetuning.py AAPL                 # Specific ticker, random date
        """
    )

    parser.add_argument(
        '--count', '-c',
        type=int,
        help='Number of random fine-tuning samples to generate'
    )

    parser.add_argument(
        'ticker',
        nargs='?',
        help='Ticker symbol (required unless using --count)'
    )

    parser.add_argument(
        'date',
        nargs='?',
        help='Target date (YYYY-MM-DD) or --random'
    )

    parser.add_argument(
        '--no-volume',
        action='store_true',
        help='Disable volume features (use price only)'
    )

    args = parser.parse_args()

    # Enable volume by default, disable if --no-volume flag is used
    use_volume = not args.no_volume

    if args.count:
        # Batch processing mode: generate multiple random samples
        print(f"[INFO] Generating {args.count} random LSTM fine-tuning samples...", file=sys.stderr)
        selections = get_random_date_and_ticker(args.count)

        success_count = 0
        for i, selection in enumerate(selections, 1):
            try:
                ticker = selection['ticker']
                company_name = selection['company_name']
                target_date = selection['date']

                print(f"[INFO] Processing {i}/{args.count}: {ticker} ({company_name}) on {target_date.strftime('%Y-%m-%d')}", file=sys.stderr)

                result = process_single_ticker(ticker, target_date, use_volume, save_to_file=True, company_name=company_name)
                if result:
                    success_count += 1

            except Exception as e:
                print(f"[ERROR] {selection['ticker']}: {e}", file=sys.stderr)

        print(f"[INFO] Completed! Successfully processed {success_count}/{args.count} samples", file=sys.stderr)

    else:
        # Single ticker mode
        if not args.ticker:
            parser.error("Ticker is required unless using --count mode")

        ticker = args.ticker.upper()

        # Determine target date
        if args.date:
            if args.date == '--random':
                target_date = get_random_target_date()
                print(f"Using random target date: {target_date}", file=sys.stderr)
            else:
                try:
                    target_date = date.fromisoformat(args.date)
                except ValueError:
                    print(f"Invalid date format: {args.date}. Use YYYY-MM-DD or --random", file=sys.stderr)
                    sys.exit(1)
        else:
            # Default to random date if no date specified
            target_date = get_random_target_date()
            print(f"Using random target date: {target_date}", file=sys.stderr)

        # Process single ticker and output to stdout
        result = process_single_ticker(ticker, target_date, use_volume, save_to_file=False)
        if result:
            print(json.dumps(result))
        else:
            sys.exit(1)


if __name__ == "__main__":
    main()
